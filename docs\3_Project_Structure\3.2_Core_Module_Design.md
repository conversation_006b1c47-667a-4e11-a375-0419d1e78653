# 3.2 核心模块设计

## 插件核心架构

EchoSync插件采用模块化的设计，确保各部分职责清晰，易于维护和扩展。

```mermaid
graph TD
    subgraph Browser
        subgraph AI_Chat_Page [AI聊天页面]
            Content_Script[Content Script]
        end
        Popup[Popup界面]
        Options[Options界面]
    end

    subgraph Extension_Process
        Background_SW[Background Service Worker]
        Storage[Chrome Storage API / IndexedDB]
    end

    Content_Script -- DOM操作 --> AI_Chat_Page
    Content_Script -- 消息通信 --> Background_SW
    Popup -- 消息通信/状态读取 --> Background_SW
    Options -- 消息通信/状态读取 --> Background_SW
    Background_SW -- 数据读写 --> Storage

    style AI_Chat_Page fill:#f9f,stroke:#333,stroke-width:2px
    style Popup fill:#ccf,stroke:#333,stroke-width:2px
    style Options fill:#ccf,stroke:#333,stroke-width:2px
```

### 1. Background Service Worker (`background/index.ts`)

作为插件的大脑，基于Manifest V3的Service Worker架构，常驻后台运行。

-   **核心职责**:
    -   **消息中枢**: 接收并分发来自Content Script、Popup、Options页面的消息
    -   **状态管理**: 维护全局状态，如当前激活的平台、同步开关、用户设置等
    -   **数据持久化**: 协调对Chrome Storage API和IndexedDB的数据读写操作
    -   **生命周期管理**: 处理插件的安装、更新、卸载等事件
    -   **权限管理**: 动态请求和管理必要的浏览器权限
    -   **快捷键处理**: 响应用户定义的键盘快捷键操作

-   **技术实现**:
    -   使用ES Modules模块系统
    -   基于事件驱动的异步处理
    -   支持热重载开发模式
    -   集成错误监控和日志记录

### 2. Content Scripts (`content/index.ts`)

注入到目标AI聊天网站的脚本，是功能实现的关键核心。

-   **核心职责**:
    -   **DOM交互**: 监听输入框变化、点击发送按钮、读取聊天记录等
    -   **提示词捕获与注入**: 实时获取用户输入的提示词，或将其他页面的提示词填充到当前页面
    -   **平台适配器调用**: 根据当前网站URL，动态加载并使用对应的AI平台适配器
    -   **浮动气泡管理**: 创建和管理提示词同步的浮动UI组件
    -   **数据同步**: 与后台脚本通信，实现跨平台的数据同步

-   **技术特性**:
    -   支持多平台适配器模式
    -   DOM变化监听和响应
    -   CSS样式隔离和注入
    -   实时数据双向绑定

### 3. AI平台适配器 (`content/adapters/`)

为了应对不同AI网站的DOM结构差异，我们设计了适配器模式。

-   **基类 `AIAdapter`**: 定义所有适配器必须实现的统一接口。
    ```typescript
    // src/content/adapters/base.ts
    export abstract class AIAdapter {
      // 平台名称，如 'ChatGPT'
      abstract platformName: string;
      
      // 各平台关键元素的CSS选择器
      abstract selectors: {
        inputField: string;  // 输入框
        sendButton: string;  // 发送按钮
        messageContainer: string; // 消息列表容器
      };

      // 将提示词注入到输入框
      abstract injectPrompt(prompt: string): Promise<void>;
      
      // 从页面提取对话内容
      abstract extractConversation(): Promise<Conversation>;
      
      // 判断当前页面是否为该平台
      abstract isValidPage(): boolean;
    }
    ```
-   **具体实现**: 为每个支持的平台（ChatGPT, Gemini等）创建一个继承自 `AIAdapter` 的具体类，实现其抽象方法。
    ```typescript
    // src/content/adapters/chatgpt.ts
    export class ChatGPTAdapter extends AIAdapter {
      platformName = 'ChatGPT';
      selectors = {
        inputField: '#prompt-textarea',
        sendButton: '[data-testid="send-button"]',
        messageContainer: '[data-testid="conversation-turn"]'
      };
      // ... 实现具体方法
    }
    ```
-   **优势**: 这种设计使得**添加对新AI平台的支持变得非常简单**，只需创建一个新的适配器文件，而无需改动核心逻辑。

### 4. 通信与存储

-   **消息通信 (`lib/messaging.ts`)**: 封装了 `chrome.runtime.sendMessage` 和 `chrome.tabs.sendMessage`，并定义了统一的消息格式（类型、载荷），实现类型安全、可预测的跨模块通信。
-   **数据存储 (`lib/storage.ts`)**: 封装了 `chrome.storage.local` API，提供简单的 `get/set` 方法。未来对于大规模历史记录，会在此模块中集成 `IndexedDB`。

---

## 🏗️ 现代化架构特性

### 数据库设计 (Dexie + IndexedDB)

项目已实现完整的客户端数据库方案，使用Dexie作为IndexedDB的现代化封装。

#### 数据库结构
```typescript
// src/lib/database/dexie.ts
export class EchoSyncDatabase extends Dexie {
  chatHistory!: Table<ChatHistory>
  platform!: Table<Platform>

  constructor() {
    super('EchoSyncDatabase')

    // 数据库版本管理
    this.version(1).stores({
      chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced',
      platform: '++id, name, url, is_delete'
    })

    // 性能优化索引
    this.version(2).stores({
      chatHistory: '++id, chat_uid, platform_id, create_time, is_delete, is_synced, [platform_id+create_time]',
      platform: '++id, name, url, is_delete'
    })
  }
}
```

#### 存储服务层
```typescript
// src/lib/storage/chatHistoryDexie.ts
export class ChatHistoryDexieService {
  async create(input: CreateChatHistoryInput): Promise<DatabaseResult<ChatHistory>> {
    try {
      await dexieDatabase.initialize()
      const data = {
        chat_prompt: input.chat_prompt,
        chat_answer: input.chat_answer || null,
        chat_uid: input.chat_uid,
        platform_id: input.platform_id,
        tags: typeof input.tags === 'string' ? input.tags : JSON.stringify(input.tags || []),
        // ... 其他字段
      }
      const id = await dexieDatabase.chatHistory.add(data)
      return { success: true, data: { ...data, id } }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }
}
```

### React Hooks 架构

#### 存储管理Hook
```typescript
// src/hooks/useStorage.ts
export function useStorage(): UseStorageReturn {
  const [state, setState] = useState<UseStorageState>({
    isConnected: false,
    isInitializing: false,
    error: null,
    lastError: null
  })

  const initialize = useCallback(async () => {
    setState(prev => ({ ...prev, isInitializing: true, error: null }))
    try {
      await dexieDatabase.initialize()
      setState(prev => ({ ...prev, isConnected: true, isInitializing: false }))
    } catch (error) {
      setState(prev => ({
        ...prev,
        isConnected: false,
        isInitializing: false,
        error: error.message
      }))
    }
  }, [])

  return { ...state, initialize }
}
```

#### 平台检测Hook
```typescript
// src/hooks/usePlatform.ts
export function usePlatform() {
  const [currentPlatform, setCurrentPlatform] = useState<Platform | null>(null)

  useEffect(() => {
    const detectPlatform = () => {
      const url = window.location.href
      // 平台检测逻辑
      if (url.includes('chat.openai.com')) {
        setCurrentPlatform({ name: 'ChatGPT', id: 1 })
      } else if (url.includes('claude.ai')) {
        setCurrentPlatform({ name: 'Claude', id: 2 })
      }
      // ... 其他平台
    }

    detectPlatform()
  }, [])

  return { currentPlatform }
}
```

### 组件设计模式

#### UI组件 (shadcn/ui)
```typescript
// src/components/ui/button.tsx
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-sm font-medium",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90",
        outline: "border border-input bg-background hover:bg-accent",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3",
        lg: "h-11 rounded-md px-8",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
```

### 构建与开发配置

#### Vite配置特性
```typescript
// vite.config.ts 关键配置
export default defineConfig(({ command, mode }) => ({
  plugins: [
    react({
      babel: {
        parserOpts: {
          plugins: ['decorators-legacy', 'classProperties']
        }
      }
    }),
    crx({
      manifest,
      contentScripts: { injectCss: true },
      browser: 'chrome'
    })
  ],
  esbuild: { target: 'es2018' },
  resolve: {
    alias: { '@': path.resolve(__dirname, './src') }
  },
  server: {
    port: 5173,
    strictPort: true,
    hmr: { port: 5174 },
    cors: { origin: true, credentials: true }
  }
}))
```

#### 热重载支持
- **开发模式**: 支持React组件热重载
- **Content Script**: 自动重新注入
- **Background Script**: 自动重启Service Worker
- **样式更新**: 实时CSS注入

### 性能优化策略

#### 代码分割
- **动态导入**: 按需加载适配器模块
- **组件懒加载**: React.lazy + Suspense
- **资源优化**: 图片压缩和字体子集化

#### 内存管理
- **数据库连接池**: 复用IndexedDB连接
- **缓存策略**: LRU缓存热点数据
- **垃圾回收**: 及时清理无用引用

#### 网络优化
- **请求合并**: 批量处理API调用
- **离线支持**: Service Worker缓存
- **增量同步**: 仅同步变更数据
