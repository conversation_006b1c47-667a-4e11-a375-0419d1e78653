# 需求
- [x]  content完成deepseek页面注入，会出现浮动气泡，
- [x]  小球支持长按拖动，拖动时变大，参见kimi的浮动小球
- [x]  当聚焦到输入框时，小球移动到当输入框的左上方，而不是上方中间
- [x]  当失去焦点后，小球回到原来的位置 
- [x]  deepseek页面新增的功能，扩充到content的adapter下的所有页面，因此每个页面除了通用的输入框探测，还支持自己页面的独有的输入框探测
- [x]  插件的content增加对kimi.com网站的支持，同时借鉴浏览器https://chat.deepseek.com页面 中kimi小球的拖动效果，改造本插件的拖动效果，如果想获得页面信息，请使用chrome-mcp-server
- [x]  实现在输入框的发送按钮左边，增加一个“存档”按钮，作用是点击后，提示词存入小球（即storage）

## 🎉 需求完成总结 (2025-01-14)

### ✅ 已完成的所有需求

#### 1. 基础功能 (已完成)
- [x] content完成deepseek页面注入，会出现浮动气泡
- [x] 小球支持长按拖动，拖动时变大，参见kimi的浮动小球
- [x] 当聚焦到输入框时，小球移动到当输入框的左上方，而不是上方中间
- [x] 当失去焦点后，小球回到原来的位置
- [x] deepseek页面新增的功能，扩充到content的adapter下的所有页面，因此每个页面除了通用的输入框探测，还支持自己页面的独有的输入框探测

#### 2. 新增需求 (已完成)
- [x] **Kimi平台支持**: 插件的content增加对kimi.com网站的支持
  - 支持 kimi.moonshot.cn 和 www.kimi.com 域名
  - 适配 contenteditable 输入框和 Lexical 编辑器
  - 实现Kimi特有的消息提取和输入框检测逻辑
  - 定制化的存档按钮样式

- [x] **拖动效果改进**: 借鉴Kimi小球的拖动效果，改造本插件的拖动效果
  - 长按时间优化：从500ms减少到300ms，响应更快
  - 添加拖拽阈值检测，避免误触发拖拽
  - 实现边界弹性效果和平滑回弹动画
  - 现代化视觉效果：紫色阴影、脉冲动画、动态缩放
  - 改进边界检测算法，支持边界约束和弹性反馈

- [x] **存档按钮完善**: 实现在输入框的发送按钮左边，增加一个"存档"按钮
  - 所有平台都支持存档按钮功能
  - 优化存档按钮的样式和位置适配
  - 改进存档成功的视觉反馈和飞行动画
  - 为不同平台定制存档按钮样式

### 🚀 技术实现亮点

#### 架构设计
- **面向对象适配器模式**: 基础AIAdapter类 + 平台特化实现
- **通用功能抽象**: 浮动气泡、拖拽、存档功能在基类中实现
- **平台特化扩展**: 每个平台保持独有的输入框检测和UI适配

#### 交互体验优化
- **智能拖拽系统**: 长按检测 + 拖拽阈值 + 边界约束
- **现代化动画**: CSS3动画 + JavaScript状态管理
- **响应式反馈**: 按下、拖拽、释放的完整交互链

#### 兼容性支持
- **多平台适配**: ChatGPT、DeepSeek、Claude、Gemini、Kimi
- **输入框检测**: textarea、contenteditable、平台特有选择器
- **移动端支持**: 触摸事件处理和响应式设计

### 📋 部署说明
1. 扩展已更新manifest.json，添加Kimi域名权限
2. 需要在Chrome扩展管理页面重新加载扩展
3. 重新加载后即可在所有支持的平台使用新功能
