import type { Conversation, Settings, User } from '@/types'

export class StorageService {
  // Chrome Storage API 封装
  static async set<T>(key: string, value: T): Promise<void> {
    try {
      await chrome.storage.local.set({ [key]: value })
    } catch (error) {
      console.error('Storage set error:', error)
      throw error
    }
  }

  static async get<T>(key: string): Promise<T | null> {
    try {
      const result = await chrome.storage.local.get(key)
      return result[key] || null
    } catch (error) {
      console.error('Storage get error:', error)
      return null
    }
  }

  static async remove(key: string): Promise<void> {
    try {
      await chrome.storage.local.remove(key)
    } catch (error) {
      console.error('Storage remove error:', error)
      throw error
    }
  }

  static async clear(): Promise<void> {
    try {
      await chrome.storage.local.clear()
    } catch (error) {
      console.error('【EchoSync】Storage clear error:', error)
      throw error
    }
  }

  // 专用方法 - 提示词相关方法已移除，现在使用chatHistoryService

  static async getConversations(): Promise<Conversation[]> {
    return (await this.get<Conversation[]>('conversations')) || []
  }

  static async saveConversations(conversations: Conversation[]): Promise<void> {
    await this.set('conversations', conversations)
  }

  static async addConversation(conversation: Conversation): Promise<void> {
    const conversations = await this.getConversations()
    conversations.unshift(conversation)
    
    // 限制数量
    if (conversations.length > 100) {
      conversations.splice(100)
    }
    
    await this.saveConversations(conversations)
  }

  static async getSettings(): Promise<Settings> {
    const defaultSettings: Settings = {
      syncEnabled: true,
      autoSync: false,
      platforms: [
        {
          id: 'chatgpt',
          name: 'ChatGPT',
          url: 'https://chat.openai.com',
          enabled: true,
          selectors: {
            inputField: '#prompt-textarea',
            sendButton: '[data-testid="send-button"]',
            messageContainer: '[data-testid="conversation-turn"]'
          }
        },
        {
          id: 'deepseek',
          name: 'DeepSeek',
          url: 'https://chat.deepseek.com',
          enabled: true,
          selectors: {
            inputField: 'textarea[placeholder*="输入"]',
            sendButton: 'button[type="submit"]',
            messageContainer: '.message'
          }
        },
        {
          id: 'claude',
          name: 'Claude',
          url: 'https://claude.ai',
          enabled: true,
          selectors: {
            inputField: 'div[contenteditable="true"]',
            sendButton: 'button[aria-label="Send Message"]',
            messageContainer: '.font-claude-message'
          }
        }
      ],
      shortcuts: {
        openPopup: 'Ctrl+Shift+E',
        quickSync: 'Ctrl+Shift+S'
      },
      theme: 'system',
      language: 'zh'
    }

    return (await this.get<Settings>('settings')) || defaultSettings
  }

  static async saveSettings(settings: Settings): Promise<void> {
    await this.set('settings', settings)
  }

  static async getUser(): Promise<User | null> {
    return await this.get<User>('user')
  }

  static async saveUser(user: User): Promise<void> {
    await this.set('user', user)
  }

  static async removeUser(): Promise<void> {
    await this.remove('user')
  }

  // 数据导出/导入
  static async exportData(): Promise<{
    conversations: Conversation[]
    settings: Settings
  }> {
    const [conversations, settings] = await Promise.all([
      this.getConversations(),
      this.getSettings()
    ])

    return { conversations, settings }
  }

  static async importData(data: {
    conversations?: Conversation[]
    settings?: Settings
  }): Promise<void> {
    if (data.conversations) await this.saveConversations(data.conversations)
    if (data.settings) await this.saveSettings(data.settings)
  }
}
