# EchoAI插件 - Prompt同步系统文档

## 📋 文档概述

本目录包含了EchoAI插件中Prompt捕捉、存储和同步功能的详细技术文档，帮助开发者理解系统的完整工作流程。

## 📁 文档结构

### 1. [prompt-sync-flowchart.md](./prompt-sync-flowchart.md)
**详细流程图文档**
- 完整的Prompt捕捉与同步流程
- 每个节点的文件名和方法名标注
- 四个主要阶段的详细说明：
  - A页面Prompt捕捉
  - IndexedDB存储详情
  - B页面历史提示词获取
  - 提示词注入到输入框
- 关键方法和事件列表
- 消息类型和自定义事件说明

### 2. [prompt-sync-mindmap.md](./prompt-sync-mindmap.md)
**系统思维导图**
- 系统架构思维导图
- 数据流思维导图
- 组件关系思维导图
- 事件流思维导图
- 错误处理思维导图
- 性能优化思维导图

### 3. [technical-implementation-flow.md](./technical-implementation-flow.md)
**技术实现详细流程**
- 系统初始化流程
- Prompt捕捉流程
- 历史提示词获取流程
- 提示词注入流程
- 关键技术实现细节
- 性能优化策略
- 错误处理机制

## 🔄 核心流程概述

### 阶段1: Prompt捕捉 (A页面)
```
用户输入 → InputManager检测 → 内容验证 → 消息发送 → Background处理 → IndexedDB存储
```

**关键文件:**
- `content/index.ts` - Content Script主入口
- `content/base/InputManager.ts` - 输入管理器
- `lib/messaging.ts` - 消息传递服务
- `background/index.ts` - Background Service Worker

### 阶段2: 数据存储
```
Background接收 → 平台识别 → 数据构造 → Dexie操作 → IndexedDB持久化
```

**关键文件:**
- `lib/storage/chatHistoryDexie.ts` - IndexedDB存储服务
- `lib/storage/platformDexie.ts` - 平台信息服务

### 阶段3: 历史获取 (B页面)
```
用户悬停 → 浮动气泡 → 历史查询 → 数据渲染 → UI显示
```

**关键文件:**
- `content/base/FloatingBubble.ts` - 浮动气泡管理
- `content/base/HistoryManager.ts` - 历史记录管理
- `components/HistoryBubble.ts` - 历史气泡UI组件

### 阶段4: 提示词注入
```
用户点击 → 事件触发 → 内容注入 → DOM更新 → 用户反馈
```

**关键文件:**
- `content/base/InputManager.ts` - 输入管理器
- `content/base/AIAdapter.ts` - AI平台适配器基类

## 🏗️ 系统架构

### 核心组件
1. **Content Scripts** - 页面内容脚本
2. **Background Service Worker** - 后台服务工作者
3. **Messaging Service** - 消息传递服务
4. **Storage Service** - 数据存储服务
5. **UI Components** - 用户界面组件

### 数据流向
```
DOM事件 → Content Script → Background Script → IndexedDB
                ↓
UI组件 ← Content Script ← Background Script ← IndexedDB
```

### 消息类型
- `SYNC_PROMPT` - 同步提示词
- `CAPTURE_PROMPT` - 捕获提示词
- `INJECT_PROMPT` - 注入提示词
- `GET_HISTORY` - 获取历史记录

## 🎯 关键技术特性

### 1. 跨平台适配
- 支持多个AI平台（ChatGPT、DeepSeek、Claude等）
- 统一的适配器接口
- 平台特定的选择器配置

### 2. 实时同步
- 跨标签页实时同步
- 防抖处理避免频繁触发
- 可配置的同步开关

### 3. 数据持久化
- IndexedDB本地存储
- Dexie.js现代化封装
- 数据去重和分页查询

### 4. 用户体验
- 浮动气泡交互
- 历史提示词快速选择
- 动画效果和视觉反馈

### 5. 性能优化
- 事件防抖和节流
- 内存管理和资源清理
- 查询优化和缓存策略

## 🔧 开发调试

### 调试工具
1. **Chrome DevTools** - 检查Content Script
2. **Extension DevTools** - 调试Background Script
3. **Console日志** - 查看详细执行信息
4. **IndexedDB查看器** - 检查存储数据

### 常用调试命令
```javascript
// 在Console中查看存储的提示词
document.dispatchEvent(new CustomEvent('echosync:show-stored-prompts'))

// 触发调试功能
document.dispatchEvent(new CustomEvent('echosync:debug-features'))
```

## 📊 性能指标

### 响应时间
- 输入捕捉延迟: < 1000ms (防抖)
- 历史查询时间: < 100ms
- 提示词注入时间: < 50ms

### 存储效率
- 数据去重率: > 90%
- 查询命中率: > 95%
- 存储空间利用率: > 80%

## 🚀 未来优化方向

1. **智能推荐** - 基于使用频率的提示词推荐
2. **云端同步** - 跨设备数据同步
3. **AI增强** - 提示词自动分类和标签
4. **性能提升** - 虚拟滚动和懒加载
5. **用户定制** - 个性化界面和快捷键

## 📝 更新日志

### v1.0.0 (当前版本)
- ✅ 基础Prompt捕捉功能
- ✅ IndexedDB存储系统
- ✅ 跨页面同步机制
- ✅ 历史提示词管理
- ✅ 浮动气泡UI组件

---

**注意**: 本文档基于当前代码结构生成，如有代码更新，请及时同步文档内容。
