import { AIAdapter } from '../base'
import { Conversation, Message, AIPlatform } from '@/types'

export class Chat<PERSON>TAdapter extends AIAdapter {
  constructor() {
    const platform = {
      name: 'ChatGPT',
      id: 'chatgpt' as AIPlatform,
      url: 'https://chat.openai.com'
    }

    const selectors = {
      inputField: '#prompt-textarea, textarea[placeholder*="Message"]',
      sendButton: 'button[data-testid="send-button"], button[aria-label="Send prompt"]',
      messageContainer: '[data-message-author-role], .group'
    }

    super(platform, selectors)
  }

  async extractConversation(): Promise<Conversation | null> {
    try {
      const messageElements = document.querySelectorAll(this.selectors.messageContainer)
      if (messageElements.length === 0) return null

      const messages: Message[] = []
      
      messageElements.forEach((element, index) => {
        const isUser = element.querySelector('[data-message-author-role="user"]') !== null
        const contentElement = element.querySelector('.markdown') || element.querySelector('[data-message-content]')
        
        if (contentElement) {
          messages.push({
            id: `msg-${index}`,
            role: isUser ? 'user' : 'assistant',
            content: contentElement.textContent || '',
            timestamp: Date.now() - (messageElements.length - index) * 1000
          })
        }
      })

      if (messages.length === 0) return null

      // 尝试获取对话标题
      const titleElement = document.querySelector('h1') || document.querySelector('[data-testid="conversation-title"]')
      const title = titleElement?.textContent || `ChatGPT对话 - ${new Date().toLocaleDateString()}`

      return {
        id: `chatgpt-${Date.now()}`,
        platform: 'chatgpt',
        title,
        messages,
        createdAt: Math.min(...messages.map(m => m.timestamp)),
        updatedAt: Math.max(...messages.map(m => m.timestamp))
      }
    } catch (error) {
      console.error('【EchoSync】Extract ChatGPT conversation error:', error)
      return null
    }
  }

  isValidPage(): boolean {
    return window.location.hostname === 'chat.openai.com' && 
           window.location.pathname.startsWith('/c/')
  }
}
