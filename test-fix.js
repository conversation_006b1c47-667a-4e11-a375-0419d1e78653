// 测试修复后的提示词存储功能
// 在浏览器控制台中运行此脚本

console.log('=== 测试提示词存储修复 ===');

// 模拟发送提示词到background
async function testPromptStorage() {
  try {
    console.log('1. 测试发送提示词到background...');
    
    // 模拟content script发送的消息
    const testPrompt = {
      content: '测试提示词：请帮我写一个JavaScript函数',
      platform: 'deepseek',
      timestamp: Date.now()
    };
    
    // 发送消息到background
    const response = await chrome.runtime.sendMessage({
      type: 'SYNC_PROMPT',
      payload: testPrompt
    });
    
    console.log('Background响应:', response);
    
    if (response.success) {
      console.log('✅ 提示词发送成功');
      
      // 等待一下让数据库操作完成
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 测试获取历史记录
      console.log('2. 测试获取历史记录...');
      const historyResponse = await chrome.runtime.sendMessage({
        type: 'GET_HISTORY',
        payload: {}
      });
      
      console.log('历史记录响应:', historyResponse);
      
      if (historyResponse.success && historyResponse.data) {
        console.log('✅ 历史记录获取成功');
        console.log('记录数量:', historyResponse.data.length);
        
        // 查找我们刚才添加的记录
        const ourRecord = historyResponse.data.find(record => 
          record.chat_prompt && record.chat_prompt.includes('测试提示词')
        );
        
        if (ourRecord) {
          console.log('✅ 找到我们的测试记录:', ourRecord);
          console.log('平台ID:', ourRecord.platform_id);
          console.log('创建时间:', new Date(ourRecord.create_time));
        } else {
          console.log('❌ 未找到我们的测试记录');
        }
      } else {
        console.log('❌ 历史记录获取失败:', historyResponse.error);
      }
    } else {
      console.log('❌ 提示词发送失败:', response.error);
    }
  } catch (error) {
    console.error('❌ 测试过程中出错:', error);
  }
}

// 测试IndexedDB直接访问
async function testIndexedDB() {
  try {
    console.log('3. 测试IndexedDB直接访问...');
    
    // 打开数据库
    const dbRequest = indexedDB.open('EchoSyncDatabase');
    
    dbRequest.onsuccess = function(event) {
      const db = event.target.result;
      console.log('✅ IndexedDB连接成功');
      console.log('数据库版本:', db.version);
      console.log('对象存储:', Array.from(db.objectStoreNames));
      
      // 查询聊天历史
      const transaction = db.transaction(['chatHistory'], 'readonly');
      const store = transaction.objectStore('chatHistory');
      const getAllRequest = store.getAll();
      
      getAllRequest.onsuccess = function() {
        const records = getAllRequest.result;
        console.log('✅ 聊天历史记录总数:', records.length);
        
        if (records.length > 0) {
          console.log('最新记录:', records[records.length - 1]);
        }
        
        // 查找测试记录
        const testRecords = records.filter(record => 
          record.chat_prompt && record.chat_prompt.includes('测试提示词')
        );
        console.log('测试记录数量:', testRecords.length);
        
        db.close();
      };
      
      getAllRequest.onerror = function() {
        console.error('❌ 查询聊天历史失败');
        db.close();
      };
    };
    
    dbRequest.onerror = function() {
      console.error('❌ IndexedDB连接失败');
    };
  } catch (error) {
    console.error('❌ IndexedDB测试出错:', error);
  }
}

// 运行测试
console.log('开始测试...');
testPromptStorage().then(() => {
  setTimeout(testIndexedDB, 2000);
});

console.log('测试脚本已启动，请查看后续输出...');
