/**
 * Favicon获取服务
 * 负责从网站获取favicon并转换为BLOB格式
 */

export interface FaviconResult {
  success: boolean
  blob?: Blob
  dataUrl?: string
  error?: string
  source?: string // 记录favicon来源
}

export class FaviconService {
  private static instance: FaviconService
  
  // favicon可能的路径，按优先级排序
  private readonly FAVICON_PATHS = [
    '/favicon.svg',
    '/favicon.png', 
    '/favicon.ico',
    '/apple-touch-icon.png',
    '/apple-touch-icon-precomposed.png',
    '/favicon-32x32.png',
    '/favicon-16x16.png'
  ]

  // 支持的MIME类型
  private readonly SUPPORTED_MIME_TYPES = [
    'image/svg+xml',
    'image/png',
    'image/x-icon',
    'image/vnd.microsoft.icon',
    'image/jpeg',
    'image/gif',
    'image/webp'
  ]

  private constructor() {}

  static getInstance(): FaviconService {
    if (!FaviconService.instance) {
      FaviconService.instance = new FaviconService()
    }
    return FaviconService.instance
  }

  /**
   * 从URL获取favicon
   */
  async getFaviconFromUrl(url: string): Promise<FaviconResult> {
    try {
      const parsedUrl = new URL(url)
      const baseUrl = `${parsedUrl.protocol}//${parsedUrl.host}`
      
      console.log(`【FaviconService】开始获取favicon: ${baseUrl}`)

      // 1. 首先尝试解析HTML中的favicon链接
      const htmlResult = await this.getFaviconFromHtml(baseUrl)
      if (htmlResult.success) {
        console.log(`【FaviconService】从HTML获取成功: ${htmlResult.source}`)
        return htmlResult
      }

      // 2. 尝试常见的favicon路径
      for (const path of this.FAVICON_PATHS) {
        const faviconUrl = baseUrl + path
        const result = await this.fetchFavicon(faviconUrl)
        if (result.success) {
          console.log(`【FaviconService】从路径获取成功: ${path}`)
          return { ...result, source: path }
        }
      }

      return {
        success: false,
        error: 'No valid favicon found'
      }

    } catch (error) {
      console.error('【FaviconService】获取favicon失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 从HTML页面解析favicon链接
   */
  private async getFaviconFromHtml(baseUrl: string): Promise<FaviconResult> {
    try {
      const response = await fetch(baseUrl, {
        method: 'GET',
        headers: {
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
      })

      if (!response.ok) {
        return { success: false, error: `HTTP ${response.status}` }
      }

      const html = await response.text()
      const faviconUrls = this.parseFaviconFromHtml(html, baseUrl)

      // 按优先级尝试获取favicon
      for (const faviconUrl of faviconUrls) {
        const result = await this.fetchFavicon(faviconUrl)
        if (result.success) {
          return { ...result, source: faviconUrl }
        }
      }

      return { success: false, error: 'No valid favicon in HTML' }

    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Failed to parse HTML' 
      }
    }
  }

  /**
   * 从HTML内容中解析favicon链接
   */
  private parseFaviconFromHtml(html: string, baseUrl: string): string[] {
    const faviconUrls: string[] = []
    
    // 匹配各种favicon相关的link标签
    const linkRegex = /<link[^>]*(?:rel=["'](?:icon|shortcut icon|apple-touch-icon)[^"']*["'])[^>]*>/gi
    const hrefRegex = /href=["']([^"']+)["']/i
    
    let match
    while ((match = linkRegex.exec(html)) !== null) {
      const linkTag = match[0]
      const hrefMatch = hrefRegex.exec(linkTag)
      
      if (hrefMatch && hrefMatch[1]) {
        let faviconUrl = hrefMatch[1]
        
        // 处理相对路径
        if (faviconUrl.startsWith('//')) {
          faviconUrl = new URL(baseUrl).protocol + faviconUrl
        } else if (faviconUrl.startsWith('/')) {
          faviconUrl = baseUrl + faviconUrl
        } else if (!faviconUrl.startsWith('http')) {
          faviconUrl = baseUrl + '/' + faviconUrl
        }
        
        faviconUrls.push(faviconUrl)
      }
    }

    return faviconUrls
  }

  /**
   * 获取favicon并转换为BLOB
   */
  private async fetchFavicon(url: string): Promise<FaviconResult> {
    try {
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Accept': 'image/*,*/*;q=0.8'
        }
      })

      if (!response.ok) {
        return { success: false, error: `HTTP ${response.status}` }
      }

      const contentType = response.headers.get('content-type') || ''
      
      // 检查是否为支持的图片类型
      if (!this.isSupportedImageType(contentType)) {
        return { success: false, error: `Unsupported content type: ${contentType}` }
      }

      const blob = await response.blob()
      
      // 验证blob大小（避免过大的文件）
      if (blob.size > 1024 * 1024) { // 1MB限制
        return { success: false, error: 'Favicon too large' }
      }

      // 创建data URL用于预览
      const dataUrl = await this.blobToDataUrl(blob)

      return {
        success: true,
        blob,
        dataUrl,
        source: url
      }

    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Fetch failed'
      }
    }
  }

  /**
   * 检查是否为支持的图片类型
   */
  private isSupportedImageType(contentType: string): boolean {
    return this.SUPPORTED_MIME_TYPES.some(type => 
      contentType.toLowerCase().includes(type.toLowerCase())
    )
  }

  /**
   * 将Blob转换为Data URL
   */
  private blobToDataUrl(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = () => reject(new Error('Failed to convert blob to data URL'))
      reader.readAsDataURL(blob)
    })
  }

  /**
   * 将Data URL转换为Blob
   */
  static dataUrlToBlob(dataUrl: string): Blob {
    const arr = dataUrl.split(',')
    const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/png'
    const bstr = atob(arr[1])
    let n = bstr.length
    const u8arr = new Uint8Array(n)
    
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n)
    }
    
    return new Blob([u8arr], { type: mime })
  }

  /**
   * 验证favicon是否有效
   */
  async validateFavicon(blob: Blob): Promise<boolean> {
    try {
      // 检查blob大小
      if (blob.size === 0 || blob.size > 1024 * 1024) {
        return false
      }

      // 检查MIME类型
      if (!this.isSupportedImageType(blob.type)) {
        return false
      }

      // 尝试创建Image对象验证
      const dataUrl = await this.blobToDataUrl(blob)
      return new Promise((resolve) => {
        const img = new Image()
        img.onload = () => resolve(true)
        img.onerror = () => resolve(false)
        img.src = dataUrl
      })

    } catch (error) {
      console.error('【FaviconService】验证favicon失败:', error)
      return false
    }
  }
}

// 导出单例实例
export const faviconService = FaviconService.getInstance()
