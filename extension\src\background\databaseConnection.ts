/**
 * Database Connection Manager
 * 管理数据库连接状态，确保Service Worker唤醒后能正确重新初始化数据库
 */

import { dexieDatabase } from '../lib/database/dexie'

export interface DatabaseConnectionStatus {
  isConnected: boolean
  isInitialized: boolean
  lastConnectionTime?: number
  connectionAttempts: number
  lastError?: string
}

export class DatabaseConnectionManager {
  private static instance: DatabaseConnectionManager | null = null
  private connectionStatus: DatabaseConnectionStatus = {
    isConnected: false,
    isInitialized: false,
    connectionAttempts: 0
  }
  private readonly MAX_RETRY_ATTEMPTS = 3
  private readonly RETRY_DELAY = 1000 // 1秒

  private constructor() {}

  /**
   * 获取单例实例
   */
  static getInstance(): DatabaseConnectionManager {
    if (!DatabaseConnectionManager.instance) {
      DatabaseConnectionManager.instance = new DatabaseConnectionManager()
    }
    return DatabaseConnectionManager.instance
  }

  /**
   * 初始化数据库连接
   */
  async initialize(): Promise<boolean> {
    console.log('【EchoSync】DatabaseConnectionManager: Starting initialization...')

    try {
      // 重置连接状态
      this.resetConnectionStatus()

      // 尝试连接数据库
      const success = await this.connectWithRetry()
      
      if (success) {
        this.connectionStatus.isInitialized = true
        this.connectionStatus.isConnected = true
        this.connectionStatus.lastConnectionTime = Date.now()
        console.log('【EchoSync】DatabaseConnectionManager: Initialization successful')
      } else {
        console.error('【EchoSync】DatabaseConnectionManager: Initialization failed after all retries')
      }

      return success
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error)
      this.connectionStatus.lastError = errorMessage
      console.error('【EchoSync】DatabaseConnectionManager: Initialization error:', error)
      return false
    }
  }

  /**
   * 带重试的数据库连接
   */
  private async connectWithRetry(): Promise<boolean> {
    for (let attempt = 1; attempt <= this.MAX_RETRY_ATTEMPTS; attempt++) {
      try {
        console.log(`【EchoSync】DatabaseConnectionManager: Connection attempt ${attempt}/${this.MAX_RETRY_ATTEMPTS}`)
        
        this.connectionStatus.connectionAttempts = attempt

        // 尝试初始化数据库
        await dexieDatabase.initialize()

        // 验证连接是否成功
        const isValid = await this.validateConnection()
        
        if (isValid) {
          console.log('【EchoSync】DatabaseConnectionManager: Connection successful')
          return true
        } else {
          throw new Error('Database connection validation failed')
        }

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        this.connectionStatus.lastError = errorMessage
        
        console.warn(`【EchoSync】DatabaseConnectionManager: Attempt ${attempt} failed:`, errorMessage)

        // 如果不是最后一次尝试，等待后重试
        if (attempt < this.MAX_RETRY_ATTEMPTS) {
          console.log(`【EchoSync】DatabaseConnectionManager: Waiting ${this.RETRY_DELAY}ms before retry...`)
          await this.delay(this.RETRY_DELAY * attempt) // 递增延迟
        }
      }
    }

    return false
  }

  /**
   * 验证数据库连接是否有效
   */
  private async validateConnection(): Promise<boolean> {
    try {
      // 尝试执行简单的数据库操作来验证连接
      const platformCount = await dexieDatabase.platform.count()
      const chatHistoryCount = await dexieDatabase.chatHistory.count()
      
      console.log('【EchoSync】DatabaseConnectionManager: Connection validation - Platforms:', platformCount, 'ChatHistory:', chatHistoryCount)
      
      return true
    } catch (error) {
      console.error('【EchoSync】DatabaseConnectionManager: Connection validation failed:', error)
      return false
    }
  }

  /**
   * 检查数据库连接状态
   */
  async checkConnection(): Promise<boolean> {
    try {
      // 如果未初始化，先尝试初始化
      if (!this.connectionStatus.isInitialized) {
        console.log('【EchoSync】DatabaseConnectionManager: Database not initialized, initializing...')
        return await this.initialize()
      }

      // 验证现有连接
      const isValid = await this.validateConnection()
      
      if (isValid) {
        this.connectionStatus.isConnected = true
        this.connectionStatus.lastConnectionTime = Date.now()
        return true
      } else {
        console.warn('【EchoSync】DatabaseConnectionManager: Connection validation failed, reconnecting...')
        return await this.reconnect()
      }

    } catch (error) {
      console.error('【EchoSync】DatabaseConnectionManager: Connection check failed:', error)
      this.connectionStatus.isConnected = false
      return false
    }
  }

  /**
   * 重新连接数据库
   */
  async reconnect(): Promise<boolean> {
    console.log('【EchoSync】DatabaseConnectionManager: Attempting to reconnect...')
    
    // 重置连接状态
    this.connectionStatus.isConnected = false
    
    // 重新初始化
    return await this.initialize()
  }

  /**
   * 确保数据库连接就绪
   * 在执行数据库操作前调用此方法
   */
  async ensureConnection(): Promise<boolean> {
    console.log('【EchoSync】DatabaseConnectionManager: Ensuring database connection...')

    // 检查连接状态
    const isConnected = await this.checkConnection()
    
    if (!isConnected) {
      console.warn('【EchoSync】DatabaseConnectionManager: Connection not ready, attempting to establish...')
      return await this.initialize()
    }

    return true
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(): DatabaseConnectionStatus {
    return { ...this.connectionStatus }
  }

  /**
   * 重置连接状态
   */
  private resetConnectionStatus(): void {
    this.connectionStatus = {
      isConnected: false,
      isInitialized: false,
      connectionAttempts: 0,
      lastError: undefined
    }
  }

  /**
   * 延迟工具函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 关闭数据库连接
   */
  async close(): Promise<void> {
    try {
      await dexieDatabase.close()
      this.resetConnectionStatus()
      console.log('【EchoSync】DatabaseConnectionManager: Database connection closed')
    } catch (error) {
      console.error('【EchoSync】DatabaseConnectionManager: Error closing database:', error)
    }
  }

  /**
   * 获取数据库健康状态
   */
  async getHealthStatus(): Promise<{
    isHealthy: boolean
    connectionStatus: DatabaseConnectionStatus
    databaseInfo?: {
      platformCount: number
      chatHistoryCount: number
    }
  }> {
    try {
      const isConnected = await this.checkConnection()
      
      let databaseInfo
      if (isConnected) {
        const platformCount = await dexieDatabase.platform.count()
        const chatHistoryCount = await dexieDatabase.chatHistory.count()
        databaseInfo = { platformCount, chatHistoryCount }
      }

      return {
        isHealthy: isConnected,
        connectionStatus: this.getConnectionStatus(),
        databaseInfo
      }
    } catch (error) {
      console.error('【EchoSync】DatabaseConnectionManager: Health check failed:', error)
      return {
        isHealthy: false,
        connectionStatus: this.getConnectionStatus()
      }
    }
  }
}

// 导出单例实例
export const databaseConnectionManager = DatabaseConnectionManager.getInstance()
