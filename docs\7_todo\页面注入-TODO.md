# DeepSeek 页面注入功能开发任务

## 需求分析
根据 `docs/需求.md` 的要求，需要实现以下功能：
1. content script 完成 DeepSeek 页面注入，会出现浮动气泡
2. 当聚焦到输入框时，小球移动到输入框上方
3. 在输入框的发送按钮左边，增加一个"存档"按钮，点击后提示词存入小球（即storage）

## 技术分析
通过分析 DeepSeek 页面源码，发现：
- 输入框：`textarea` 元素，可能的选择器包括 `textarea[placeholder*="输入"]`
- 发送按钮：位于输入框右侧的按钮元素
- 页面使用了复杂的 CSS 类名和结构

## 任务分解

### [x] 任务1：创建 DeepSeek 适配器
- [x] 1.1 在 `extension/src/content/adapters/` 目录下创建 `deepseek-adapter.ts`
- [x] 1.2 实现 DeepSeek 页面的元素选择器识别
- [x] 1.3 定义输入框、发送按钮等关键元素的选择器

### [x] 任务2：实现浮动气泡组件
- [x] 2.1 创建浮动气泡组件（使用原生DOM）
- [x] 2.2 实现气泡的基本样式和动画
- [ ] 2.3 添加气泡的拖拽功能（可选）

### [x] 任务3：实现输入框聚焦检测
- [x] 3.1 监听输入框的 focus 和 blur 事件
- [x] 3.2 实现气泡移动到输入框上方的动画
- [ ] 3.3 处理页面滚动时气泡位置的更新

### [x] 任务4：实现存档按钮
- [x] 4.1 在发送按钮左侧插入存档按钮
- [x] 4.2 设计存档按钮的样式，与页面风格保持一致
- [x] 4.3 实现按钮的点击事件处理

### [x] 任务5：实现提示词存储功能
- [x] 5.1 获取输入框中的文本内容
- [x] 5.2 调用 storage API 保存提示词
- [x] 5.3 添加保存成功的视觉反馈

### [x] 任务6：集成到 content script
- [x] 6.1 修改 `extension/src/content/index.ts`
- [x] 6.2 添加 DeepSeek 域名检测
- [x] 6.3 初始化 DeepSeek 适配器

### [/] 任务7：测试和优化
- [x] 7.1 构建项目成功
- [ ] 7.2 在 DeepSeek 页面测试功能
- [ ] 7.3 处理页面动态加载的情况
- [ ] 7.4 优化性能和用户体验

## 开发优先级
1. 高优先级：任务1、任务6（基础架构）
2. 中优先级：任务2、任务3（核心功能）
3. 低优先级：任务4、任务5（存档功能）
4. 测试优先级：任务7（测试优化）

## 技术要点
- 使用 MutationObserver 监听页面动态变化
- 使用 CSS-in-JS 或 styled-components 确保样式不冲突
- 使用 Chrome Extension Storage API 进行数据持久化
- 考虑页面的 CSP（内容安全策略）限制

## 预期完成时间
- 任务1-2：2小时
- 任务3-4：3小时
- 任务5-6：2小时
- 任务7：1小时
- 总计：8小时

## 已完成功能总结

### ✅ 核心功能实现
1. **浮动气泡组件**
   - 创建了一个美观的圆形浮动气泡，位于页面右上角
   - 使用渐变背景和阴影效果，提供良好的视觉体验
   - 支持悬停动画效果（缩放和阴影变化）

2. **输入框聚焦检测**
   - 监听 DeepSeek 输入框的 focus 和 blur 事件
   - 当聚焦时，气泡平滑移动到输入框上方居中位置
   - 失焦时，气泡返回默认的右上角位置

3. **存档按钮**
   - 在发送按钮左侧添加了存档按钮
   - 使用文档图标，样式与页面保持一致
   - 支持悬停效果和点击反馈

4. **提示词存储功能**
   - 点击存档按钮可将当前输入框内容保存到 Chrome Storage
   - 支持最多存储100条提示词，超出时自动删除最旧的
   - 提供存档成功的视觉反馈和飞行动画

5. **用户体验优化**
   - 添加了多种通知类型（成功、错误、信息）
   - 实现了存档按钮到气泡的飞行动画效果
   - 所有交互都有适当的视觉反馈

### ✨ 最新功能实现

#### 🎯 拖拽功能
1. **长按拖拽**
   - 长按500ms后进入拖拽模式
   - 拖拽时气泡放大1.3倍，增强阴影效果
   - 支持鼠标和触摸事件（移动端兼容）

2. **智能边界检测**
   - 拖拽时自动限制在视口范围内
   - 防止气泡移出屏幕边界

3. **位置记忆**
   - 拖拽结束后更新原始位置
   - 失焦时回到最后拖拽的位置

#### 🎯 聚焦行为优化
1. **精确定位**
   - 聚焦时移动到输入框左上方
   - 失焦时回到用户最后设置的位置

2. **视觉反馈**
   - 聚焦时气泡轻微放大
   - 拖拽时显著放大并增强阴影

### 🏗️ 架构重构亮点

#### 📦 基础类增强 (base.ts)
- **通用UI组件**: 浮动气泡、存档按钮、拖拽功能
- **智能输入框检测**: 通用选择器 + 平台特有选择器
- **事件管理**: 统一的拖拽、聚焦、存档事件处理
- **位置记忆**: 自动保存和恢复用户自定义位置

#### 🎯 平台特化支持
- **DeepSeek**: 中文输入框、特有按钮选择器
- **ChatGPT**: prompt-textarea、data-testid选择器
- **Claude**: contenteditable、role属性选择器
- **Gemini**: rich-textarea、ql-editor选择器

#### 🔄 继承机制
```typescript
AIAdapter (基础类)
├── 通用功能: 气泡、拖拽、存档
├── 通用选择器: textarea、contenteditable等
└── 抽象方法: injectPrompt、extractConversation

PlatformAdapter (平台类)
├── 继承所有通用功能
├── 重写 getUniversalInputSelectors()
└── 实现平台特有的抽象方法
```

### 🔧 技术实现亮点
- 使用 TypeScript 确保类型安全
- 采用 MutationObserver 监听页面动态变化
- 使用 Chrome Extension Storage API 进行数据持久化
- 实现了平滑的 CSS 动画和过渡效果
- 考虑了 z-index 层级避免与页面元素冲突
- **新增**: 长按检测和拖拽状态管理
- **新增**: 触摸事件支持，移动端友好
- **新增**: 位置记忆和智能回归功能
- **新增**: 面向对象的适配器架构
- **新增**: 平台特化的输入框检测策略

### 🆕 新增需求
- [x] 小球支持长按拖动，拖动时变大，参见kimi的浮动小球
- [x] 当聚焦到输入框时，小球移动到输入框的左上方，而不是上方中间
- [x] 当失去焦点后，小球回到原来的位置

### 🚀 通用功能扩展
- [x] 将DeepSeek的新功能扩展到所有AI平台适配器
- [x] 重构基础适配器类，添加通用UI组件支持
- [x] 每个平台保持独有的输入框检测能力
- [x] 所有平台现在都支持浮动气泡、拖拽、存档功能

### 🆕 最新需求完成 (2025-01-14)

#### ✅ Kimi平台支持
1. **新增Kimi适配器**
   - 支持 kimi.moonshot.cn 和 kimi.com 域名
   - 适配 contenteditable 输入框和 Lexical 编辑器
   - 实现特有的消息提取逻辑
   - 定制化的存档按钮样式

2. **输入框检测优化**
   - 支持 `.chat-input-editor[contenteditable="true"]`
   - 支持 `[data-lexical-editor="true"]` 选择器
   - 智能处理 contenteditable 元素的文本注入

#### ✅ 拖动效果重大改进
1. **更流畅的交互体验**
   - 长按时间从500ms优化到300ms，响应更快
   - 添加拖拽阈值检测，避免误触
   - 实现边界弹性效果和回弹动画

2. **现代化视觉效果**
   - 按下时的缩放反馈 (scale 0.95)
   - 拖拽时的动态阴影和脉冲动画
   - 边界回弹使用 cubic-bezier 缓动函数
   - 紫色主题的阴影效果

3. **技术改进**
   - 分离长按状态和拖拽状态管理
   - 优化边界检测算法
   - 添加过渡动画控制

### 🔧 最新改进（针对气泡移动问题）

1. **增强输入框检测**
   - 扩展了输入框选择器，支持更多类型的输入元素
   - 使用事件委托监听 `focusin` 和 `focusout` 事件
   - 添加了 MutationObserver 监听页面动态变化

2. **改进气泡移动逻辑**
   - 添加了边界检查，确保气泡不会移出视口
   - 增加了详细的调试日志
   - 添加了聚焦时的缩放效果

3. **调试功能**
   - 右键点击气泡可输出调试信息
   - 可以手动测试气泡移动功能
   - 显示所有找到的输入元素

## 🆕 未完成需求实现计划

### 需求1: 增加对Kimi.com网站的支持 ✅
- [x] 创建 Kimi 适配器 (kimi.ts)
- [x] 分析 Kimi 页面结构和输入框选择器
- [x] 实现 Kimi 特有的输入框检测逻辑
- [x] 集成到主 content script
- [x] 添加 Kimi 平台类型定义

### 需求2: 改进拖动效果（借鉴Kimi小球） ✅
- [x] 分析 Kimi 小球的拖动效果特点
- [x] 改进现有拖动动画和交互
- [x] 优化拖动时的视觉反馈
- [x] 添加边界回弹效果
- [x] 改进长按检测和拖拽阈值
- [x] 增加脉冲动画和现代化视觉效果

### 需求3: 完善存档按钮功能 ✅
- [x] 确保所有平台都有存档按钮
- [x] 优化存档按钮的样式和位置
- [x] 改进存档成功的视觉反馈
- [x] 为 Kimi 平台定制存档按钮样式

## 📋 测试指南

**多平台测试步骤：**

### 🎯 通用测试流程

1. **加载扩展**
   - 将 `extension/dist` 目录加载到 Chrome 扩展程序

2. **基础功能验证**
   - [ ] 确认右上角出现紫色渐变的浮动气泡
   - [ ] 点击气泡查看存档提示词功能

3. **拖拽功能测试**
   - [ ] 长按气泡500ms，观察是否进入拖拽模式（变大）
   - [ ] 拖拽气泡到不同位置，观察边界限制
   - [ ] 释放后观察气泡是否恢复正常大小

4. **聚焦移动测试**
   - [ ] 点击输入框，观察气泡是否移动到输入框左上方
   - [ ] 点击页面其他地方，观察气泡是否返回拖拽后的位置

5. **存档功能测试**
   - [ ] 在输入框输入文字
   - [ ] 查看发送按钮左侧是否出现存档按钮
   - [ ] 点击存档按钮测试保存功能

## 🚀 部署和测试指南

### 重新加载扩展（必需步骤）
由于添加了新的域名支持，需要重新加载扩展：

1. **打开Chrome扩展管理页面**
   - 在地址栏输入 `chrome://extensions/`
   - 或者点击Chrome菜单 → 更多工具 → 扩展程序

2. **重新加载EchoSync扩展**
   - 找到"EchoSync - AI提示词同步器"扩展
   - 点击扩展卡片上的"重新加载"按钮（🔄图标）
   - 确保扩展状态为"已启用"

3. **验证权限**
   - 点击扩展的"详细信息"
   - 确认"网站访问权限"包含：
     - `https://kimi.moonshot.cn/*`
     - `https://www.kimi.com/*`

### 测试新功能

#### 🎯 Kimi平台测试
1. **访问Kimi网站**
   - 打开 https://www.kimi.com 或 https://kimi.moonshot.cn
   - 进入任意对话页面

2. **验证扩展加载**
   - 确认右上角出现紫色渐变的浮动气泡
   - 打开开发者工具，查看控制台是否有"EchoSync Content Script loaded"日志

3. **测试改进的拖动效果**
   - 长按气泡300ms，观察是否进入拖拽模式
   - 拖拽时气泡应该：
     - 放大到1.2倍
     - 显示紫色阴影和脉冲动画
     - 支持边界弹性效果
   - 释放后观察边界回弹动画

4. **测试输入框检测**
   - 点击Kimi的输入框（contenteditable元素）
   - 观察气泡是否移动到输入框左上方
   - 点击页面其他地方，气泡应返回拖拽后的位置

5. **测试存档功能**
   - 在输入框输入文字
   - 查看发送按钮左侧是否出现存档按钮
   - 点击存档按钮测试保存功能

## 📊 完成情况总结

### ✅ 已完成的主要功能
1. **多平台支持** - 支持ChatGPT、DeepSeek、Claude、Gemini、Kimi
2. **智能拖拽** - 长按拖拽、边界回弹、现代化视觉效果
3. **输入框检测** - 通用选择器 + 平台特化检测
4. **存档功能** - 提示词保存、飞行动画、成功反馈
5. **聚焦移动** - 输入框聚焦时气泡智能定位

### 🎨 视觉和交互改进
1. **拖拽体验优化**
   - 响应时间从500ms优化到300ms
   - 添加按下反馈和拖拽阈值
   - 边界弹性效果和回弹动画
   - 紫色主题阴影和脉冲动画

2. **现代化设计**
   - 渐变背景和动态阴影
   - 流畅的过渡动画
   - 响应式交互反馈

### 🔧 技术架构亮点
1. **面向对象设计** - 基础适配器类 + 平台特化
2. **事件管理优化** - 分离状态管理和事件处理
3. **动画系统** - CSS动画 + JavaScript控制
4. **边界检测算法** - 智能约束和弹性效果
