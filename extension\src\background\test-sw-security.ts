/**
 * Service Worker Security Test Script
 * 用于测试Service Worker安全性机制的脚本
 */

import { MessagingService } from '../lib/service/messaging'

export class ServiceWorkerSecurityTest {
  /**
   * 测试Service Worker保活机制
   */
  static async testKeepAlive(): Promise<void> {
    console.log('【Test】Testing Service Worker keep-alive mechanism...')
    
    try {
      // 测试SW状态检查
      const isAlive = await MessagingService.checkServiceWorkerAlive()
      console.log('【Test】SW alive check:', isAlive ? '✓ Pass' : '✗ Fail')
      
      // 测试SW唤醒
      await MessagingService.wakeUpServiceWorker()
      console.log('【Test】SW wake-up: ✓ Pass')
      
      // 测试确保SW就绪
      await MessagingService.ensureServiceWorkerReady()
      console.log('【Test】SW ready check: ✓ Pass')
      
    } catch (error) {
      console.error('【Test】Keep-alive test failed:', error)
    }
  }

  /**
   * 测试数据库连接安全性
   */
  static async testDatabaseSecurity(): Promise<void> {
    console.log('【Test】Testing database connection security...')
    
    try {
      // 测试数据库操作
      const testMessage = {
        type: 'DB_PLATFORM_GET_LIST' as any,
        payload: {},
        timestamp: Date.now()
      }
      
      const response = await MessagingService.sendToBackground(testMessage.type, testMessage.payload)
      console.log('【Test】Database operation:', response.success ? '✓ Pass' : '✗ Fail')
      
    } catch (error) {
      console.error('【Test】Database security test failed:', error)
    }
  }

  /**
   * 测试健康监控
   */
  static async testHealthMonitoring(): Promise<void> {
    console.log('【Test】Testing health monitoring...')
    
    try {
      // 测试健康检查
      const healthResponse = await chrome.runtime.sendMessage({ type: 'SW_HEALTH_CHECK' })
      console.log('【Test】Health check:', healthResponse.success ? '✓ Pass' : '✗ Fail')
      
      // 测试系统状态
      const statusResponse = await chrome.runtime.sendMessage({ type: 'SW_SYSTEM_STATUS' })
      console.log('【Test】System status:', statusResponse.success ? '✓ Pass' : '✗ Fail')
      
      if (statusResponse.success) {
        console.log('【Test】System status data:', statusResponse.data)
      }
      
    } catch (error) {
      console.error('【Test】Health monitoring test failed:', error)
    }
  }

  /**
   * 测试消息重试机制
   */
  static async testMessageRetry(): Promise<void> {
    console.log('【Test】Testing message retry mechanism...')
    
    try {
      // 发送多个消息测试重试机制
      const promises = []
      for (let i = 0; i < 5; i++) {
        promises.push(
          MessagingService.sendToBackground('DB_PLATFORM_GET_LIST' as any, {})
        )
      }
      
      const results = await Promise.allSettled(promises)
      const successCount = results.filter(r => r.status === 'fulfilled').length
      
      console.log(`【Test】Message retry: ${successCount}/5 messages succeeded`)
      
    } catch (error) {
      console.error('【Test】Message retry test failed:', error)
    }
  }

  /**
   * 运行所有测试
   */
  static async runAllTests(): Promise<void> {
    console.log('【Test】Starting Service Worker security tests...')
    
    await this.testKeepAlive()
    await this.testDatabaseSecurity()
    await this.testHealthMonitoring()
    await this.testMessageRetry()
    
    console.log('【Test】All tests completed')
  }

  /**
   * 压力测试 - 模拟Service Worker挂起后的恢复
   */
  static async stressTest(): Promise<void> {
    console.log('【Test】Starting stress test...')
    
    try {
      // 发送大量消息来测试系统稳定性
      const messageCount = 20
      const promises = []
      
      for (let i = 0; i < messageCount; i++) {
        promises.push(
          MessagingService.sendToBackground('DB_PLATFORM_GET_LIST' as any, {})
            .then(() => ({ success: true, index: i }))
            .catch(error => ({ success: false, index: i, error }))
        )
        
        // 添加随机延迟
        await new Promise(resolve => setTimeout(resolve, Math.random() * 100))
      }
      
      const results = await Promise.all(promises)
      const successCount = results.filter(r => r.success).length
      const failureCount = results.filter(r => !r.success).length
      
      console.log(`【Test】Stress test results: ${successCount} success, ${failureCount} failures`)
      
      if (failureCount > 0) {
        console.warn('【Test】Failed messages:', results.filter(r => !r.success))
      }
      
    } catch (error) {
      console.error('【Test】Stress test failed:', error)
    }
  }
}

// 在开发环境中自动运行测试
if (process.env.NODE_ENV === 'development') {
  // 延迟执行测试，确保Service Worker完全初始化
  setTimeout(() => {
    ServiceWorkerSecurityTest.runAllTests()
  }, 3000)
}
