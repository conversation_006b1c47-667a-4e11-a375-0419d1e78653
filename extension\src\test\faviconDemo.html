<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Favicon功能演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            border-radius: 8px;
            padding: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1, h2 {
            color: #333;
            margin-top: 0;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .button:hover {
            background: #0056b3;
        }
        
        .button.secondary {
            background: #6c757d;
        }
        
        .button.success {
            background: #28a745;
        }
        
        .button.danger {
            background: #dc3545;
        }
        
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .platform-icons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        .platform-item {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            background: #f8f9fa;
            border-radius: 4px;
            border: 1px solid #dee2e6;
        }
        
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.loading {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: #007bff;
            transition: width 0.3s ease;
        }
        
        .log {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Favicon功能演示</h1>
        <p>这个页面用于测试和演示favicon获取、BLOB存储和图标显示功能。</p>
    </div>

    <div class="container">
        <div class="section">
            <h2>🔧 基础功能测试</h2>
            <button class="button" onclick="testFaviconService()">测试Favicon服务</button>
            <button class="button" onclick="testIconBlobService()">测试BLOB服务</button>
            <button class="button" onclick="testPlatformService()">测试平台服务</button>
            <button class="button secondary" onclick="runAllTests()">运行所有测试</button>
            <div id="testResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <div class="section">
            <h2>🔄 数据迁移</h2>
            <button class="button" onclick="checkMigration()">检查迁移需求</button>
            <button class="button success" onclick="startMigration()">开始迁移</button>
            <button class="button danger" onclick="forceMigration()">强制重新迁移</button>
            
            <div id="migrationStatus" style="display: none;">
                <div class="progress-bar">
                    <div id="migrationProgress" class="progress-fill" style="width: 0%;"></div>
                </div>
                <div id="migrationInfo" class="status loading">准备中...</div>
            </div>
            
            <div id="migrationResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <div class="section">
            <h2>🖼️ 平台图标展示</h2>
            <button class="button" onclick="loadPlatformIcons()">加载平台图标</button>
            <button class="button secondary" onclick="refreshIcons()">刷新图标</button>
            <div id="platformIcons" class="platform-icons"></div>
        </div>
    </div>

    <div class="container">
        <div class="section">
            <h2>📊 系统状态</h2>
            <button class="button" onclick="showSystemStatus()">显示系统状态</button>
            <button class="button secondary" onclick="clearCache()">清除缓存</button>
            <div id="systemStatus" class="result" style="display: none;"></div>
        </div>
    </div>

    <div class="container">
        <div class="section">
            <h2>📝 实时日志</h2>
            <button class="button secondary" onclick="clearLog()">清除日志</button>
            <div id="log" class="log"></div>
        </div>
    </div>

    <script>
        // 日志功能
        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.textContent += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
        }

        // 显示结果
        function showResult(elementId, content) {
            const element = document.getElementById(elementId);
            element.textContent = content;
            element.style.display = 'block';
        }

        // 测试函数（这些需要在实际环境中实现）
        async function testFaviconService() {
            log('开始测试Favicon服务...');
            showResult('testResult', '测试功能需要在Chrome扩展环境中运行');
        }

        async function testIconBlobService() {
            log('开始测试BLOB服务...');
            showResult('testResult', '测试功能需要在Chrome扩展环境中运行');
        }

        async function testPlatformService() {
            log('开始测试平台服务...');
            showResult('testResult', '测试功能需要在Chrome扩展环境中运行');
        }

        async function runAllTests() {
            log('开始运行所有测试...');
            showResult('testResult', '测试功能需要在Chrome扩展环境中运行\n\n在扩展环境中，请在控制台运行：\nfaviconTest.runAllTests()');
        }

        async function checkMigration() {
            log('检查迁移需求...');
            showResult('migrationResult', '迁移功能需要在Chrome扩展环境中运行');
        }

        async function startMigration() {
            log('开始数据迁移...');
            document.getElementById('migrationStatus').style.display = 'block';
            
            // 模拟进度
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                document.getElementById('migrationProgress').style.width = progress + '%';
                document.getElementById('migrationInfo').textContent = `迁移进度: ${progress}%`;
                
                if (progress >= 100) {
                    clearInterval(interval);
                    document.getElementById('migrationInfo').textContent = '迁移完成';
                    document.getElementById('migrationInfo').className = 'status success';
                    showResult('migrationResult', '迁移功能需要在Chrome扩展环境中运行');
                }
            }, 200);
        }

        async function forceMigration() {
            log('开始强制重新迁移...');
            startMigration();
        }

        async function loadPlatformIcons() {
            log('加载平台图标...');
            const container = document.getElementById('platformIcons');
            container.innerHTML = `
                <div class="platform-item">
                    <div style="width: 20px; height: 20px; background: #ddd; border-radius: 4px;"></div>
                    <span>DeepSeek</span>
                    <span class="status success">已加载</span>
                </div>
                <div class="platform-item">
                    <div style="width: 20px; height: 20px; background: #ddd; border-radius: 4px;"></div>
                    <span>ChatGPT</span>
                    <span class="status success">已加载</span>
                </div>
                <div class="platform-item">
                    <div style="width: 20px; height: 20px; background: #ddd; border-radius: 4px;"></div>
                    <span>Claude</span>
                    <span class="status loading">加载中</span>
                </div>
            `;
        }

        async function refreshIcons() {
            log('刷新图标...');
            loadPlatformIcons();
        }

        async function showSystemStatus() {
            log('获取系统状态...');
            const status = `系统状态信息：
数据库连接: ✓ 正常
缓存状态: ✓ 正常
平台数量: 5
BLOB存储: 3/5 已迁移
内存使用: 正常

注意：实际状态需要在Chrome扩展环境中查看`;
            showResult('systemStatus', status);
        }

        async function clearCache() {
            log('清除缓存...');
            showResult('systemStatus', '缓存已清除');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('Favicon功能演示页面已加载');
            log('请在Chrome扩展环境中使用完整功能');
        });
    </script>
</body>
</html>
