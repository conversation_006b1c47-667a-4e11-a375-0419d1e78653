import { DOMUtils } from './DOMUtils'

/**
 * 输入检测配置接口
 */
export interface InputDetectionConfig {
  selectors: string[]
  patterns?: RegExp[]
  customValidator?: (element: HTMLElement) => boolean
  contentExtractor?: (element: HTMLElement) => string
}

/**
 * 输入管理器配置接口
 */
export interface InputManagerConfig {
  inputField: string
  sendButton: string
  messageContainer?: string
  detection?: InputDetectionConfig
}

/**
 * 输入管理类
 * 重构后的纯粹输入管理实现类，支持配置化扩展
 */
export class InputManager {
  private inputElement: HTMLElement | null = null
  private sendButtonContainer: HTMLElement | null = null
  private lastInputValue: string = ''
  private config: InputManagerConfig | null = null

  constructor(config?: InputManagerConfig) {
    this.config = config || null
  }

  /**
   * 通用输入元素检测方法
   */
  detectInputElement(config?: InputDetectionConfig): HTMLElement | null {
    const detectionConfig = config || this.config?.detection
    if (!detectionConfig) {
      // 使用默认的选择器检测
      const selector = this.config?.inputField || 'input, textarea, [contenteditable="true"]'
      return document.querySelector(selector)
    }

    // 使用配置化检测
    for (const selector of detectionConfig.selectors) {
      const elements = document.querySelectorAll(selector)
      for (const element of elements) {
        const htmlElement = element as HTMLElement

        // 检查正则模式
        if (detectionConfig.patterns) {
          const text = htmlElement.textContent || htmlElement.getAttribute('placeholder') || ''
          const matches = detectionConfig.patterns.some(pattern => pattern.test(text))
          if (!matches) continue
        }

        // 检查自定义验证器
        if (detectionConfig.customValidator && !detectionConfig.customValidator(htmlElement)) {
          continue
        }

        return htmlElement
      }
    }

    return null
  }

  /**
   * 通用内容提取方法
   */
  extractContent(element: HTMLElement, config?: InputDetectionConfig): string {
    const detectionConfig = config || this.config?.detection

    // 使用自定义提取器
    if (detectionConfig?.contentExtractor) {
      return detectionConfig.contentExtractor(element)
    }

    // 使用默认提取逻辑
    if (element.tagName === 'TEXTAREA' || element.tagName === 'INPUT') {
      return (element as HTMLInputElement).value
    } else if (element.contentEditable === 'true') {
      return element.textContent || ''
    }

    return ''
  }

  /**
   * 设置输入聚焦监听
   */
  setupInputFocusListener(selectors: { inputField: string }): void {
    // 更新配置
    if (!this.config) {
      this.config = { inputField: selectors.inputField, sendButton: '' }
    } else {
      this.config.inputField = selectors.inputField
    }

    // 监听全局点击事件
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement
      if (target && DOMUtils.isInputElement(target)) {
        // 检查是否匹配选择器
        if (target.matches(selectors.inputField)) {
          this.inputElement = target
          console.log('【EchoSync】Input element focused:', target)

          // 设置输入监听
          this.setupInputMonitoring()

          // 触发输入框聚焦事件
          document.dispatchEvent(new CustomEvent('echosync:input-focused', {
            detail: { inputElement: target }
          }))
        }
      }
    })

    // 监听焦点事件
    document.addEventListener('focusin', (e) => {
      const target = e.target as HTMLElement
      if (target && target.matches(selectors.inputField)) {
        this.inputElement = target
        console.log('【EchoSync】Input element focused via focusin:', target)

        // 设置输入监听
        this.setupInputMonitoring()

        // 触发输入框聚焦事件
        document.dispatchEvent(new CustomEvent('echosync:input-focused', {
          detail: { inputElement: target }
        }))
      }
    })

    console.log('【EchoSync】Input focus listener set up')
  }

  /**
   * 查找并设置输入元素
   */
  async findAndSetupInputElement(selectors: { inputField: string }): Promise<HTMLElement | null> {
    // 首先尝试直接查找
    let inputElement = document.querySelector(selectors.inputField) as HTMLElement
    
    if (!inputElement) {
      // 如果没找到，尝试通用选择器
      const universalSelectors = DOMUtils.getUniversalInputSelectors()
      for (const selector of universalSelectors) {
        inputElement = document.querySelector(selector) as HTMLElement
        if (inputElement && DOMUtils.isVisibleElement(inputElement)) {
          break
        }
      }
    }

    if (!inputElement) {
      // 等待元素出现
      inputElement = await DOMUtils.waitForElement(selectors.inputField) as HTMLElement
    }

    if (inputElement) {
      this.inputElement = inputElement
      console.log('【EchoSync】Input element found and set:', inputElement)
      
      // 设置输入监听
      this.setupInputMonitoring()
    }

    return inputElement
  }

  /**
   * 设置输入监听
   */
  private setupInputMonitoring(): void {
    if (!this.inputElement) return

    // 监听输入变化
    const handleInput = () => {
      const currentValue = this.getCurrentInput()
      if (currentValue !== this.lastInputValue) {
        this.lastInputValue = currentValue
        
        // 触发输入变化事件
        document.dispatchEvent(new CustomEvent('echosync:input-changed', {
          detail: { value: currentValue }
        }))
      }
    }

    this.inputElement.addEventListener('input', handleInput)
    this.inputElement.addEventListener('change', handleInput)
    this.inputElement.addEventListener('keyup', handleInput)

    console.log('【EchoSync】Input monitoring set up')
  }

  /**
   * 设置发送监听
   */
  setupSendListener(selectors: { sendButton: string }): void {
    // 监听键盘事件（Enter键）
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Enter' && !e.shiftKey && this.inputElement && 
          document.activeElement === this.inputElement) {
        
        // 检查是否可以发送
        if (DOMUtils.canSendMessage(selectors)) {
          console.log('【EchoSync】Send triggered by Enter key')
          this.handleSendEvent()
        }
      }
    })

    // 监听发送按钮点击
    document.addEventListener('click', (e) => {
      const target = e.target as HTMLElement
      
      if (this.isSendButton(target, selectors.sendButton)) {
        console.log('【EchoSync】Send triggered by button click on:', target)
        this.handleSendEvent()
      } else {
        // 检查是否点击了发送按钮相关的元素
        const sendButton = target.closest('.send-button')
        const sendContainer = target.closest('.send-button-container')

        if (sendButton || sendContainer) {
          console.log('【EchoSync】Container disabled:', sendContainer?.classList.contains('disabled'))
        }
      }
    })

    console.log('【EchoSync】Send listener set up')
  }

  /**
   * 检查是否是发送按钮
   */
  private isSendButton(element: HTMLElement, sendButtonSelector: string): boolean {
    if (!element) {
      console.log('【EchoSync】isSendButton: No element provided')
      return false
    }

    // 检查元素本身
    if (element.matches(sendButtonSelector)) {
      
      // 对于Kimi，还需要检查容器是否被禁用
      const container = element.closest('.send-button-container')
      if (container && container.classList.contains('disabled')) {
        console.log('【EchoSync】isSendButton: Send button container is disabled')
        return false
      }
      
      return true
    }

    // 检查父元素（最多向上查找5层，因为Kimi的结构较深）
    
    let parent = element.parentElement
    let depth = 0
    while (parent && depth < 5) {
      
      if (parent.matches(sendButtonSelector)) {
        
        // 同样检查容器状态
        const container = parent.closest('.send-button-container')
        if (container && container.classList.contains('disabled')) {
          console.log('【EchoSync】isSendButton: Send button parent container is disabled')
          return false
        }
        
        return true
      }
      parent = parent.parentElement
      depth++
    }

    // 检查是否点击的是发送按钮的子元素（如SVG图标）
    
    const closestSendButton = element.closest('.send-button')
    if (closestSendButton) {
      const sendButton = closestSendButton as HTMLElement
      const container = sendButton.closest('.send-button-container')
      if (container && container.classList.contains('disabled')) {
        console.log('【EchoSync】isSendButton: Send button (from child) container is disabled')
        return false
      }
      
      return true
    }

    // 检查常见的发送按钮特征
    
    const text = element.textContent?.toLowerCase() || ''
    const ariaLabel = element.getAttribute('aria-label')?.toLowerCase() || ''

    const hasTextFeatures = text.includes('send') || text.includes('发送') ||
                           ariaLabel.includes('send') || ariaLabel.includes('发送')

    return hasTextFeatures
  }

  /**
   * 处理发送事件
   */
  private async handleSendEvent(): Promise<void> {
    console.log('【EchoSync】InputManager handleSendEvent triggered')

    // 获取发送前的输入内容
    const promptContent = this.getCurrentInput()
    console.log('【EchoSync】Captured prompt content:', promptContent)

    // 触发发送前捕捉事件，让AIAdapter处理存档
    if (promptContent && promptContent.trim().length > 0) {
      document.dispatchEvent(new CustomEvent('echosync:prompt-send', {
        detail: { prompt: promptContent }
      }))
    }

    // 延迟检查是否有新消息出现
    setTimeout(() => {
      console.log('【EchoSync】Dispatching check-new-message event')
      document.dispatchEvent(new CustomEvent('echosync:check-new-message'))
    }, 1000)
  }

  /**
   * 获取当前输入内容
   */
  getCurrentInput(): string {
    // 如果没有设置输入元素，尝试自动检测
    if (!this.inputElement) {
      this.inputElement = this.detectInputElement()
    }

    if (!this.inputElement) return ''

    // 使用配置化的内容提取
    return this.extractContent(this.inputElement)
  }

  /**
   * 注入提示词到输入框
   */
  injectPrompt(prompt: string): void {
    if (!this.inputElement) {
      console.warn('【EchoSync】No input element found for prompt injection')
      return
    }

    DOMUtils.simulateUserInput(this.inputElement, prompt)
    console.log('【EchoSync】Prompt injected:', prompt)
  }

  /**
   * 获取输入元素
   */
  getInputElement(): HTMLElement | null {
    return this.inputElement
  }

  /**
   * 获取发送按钮容器
   */
  getSendButtonContainer(): HTMLElement | null {
    return this.sendButtonContainer
  }

  /**
   * 设置发送按钮容器
   */
  setSendButtonContainer(container: HTMLElement): void {
    this.sendButtonContainer = container
  }

  /**
   * 获取最后输入值
   */
  getLastInputValue(): string {
    return this.lastInputValue
  }

  /**
   * 销毁
   */
  destroy(): void {
    this.inputElement = null
    this.sendButtonContainer = null
    this.lastInputValue = ''
  }
}
