import { dexieDatabase } from '../database/dexie'
import {
  Platform,
  CreatePlatformInput,
  UpdatePlatformInput,
  DatabaseResult
} from '../../types/database'
import { faviconService } from './faviconService'

export class PlatformService {
  private static instance: PlatformService

  public static getInstance(): PlatformService {
    if (!PlatformService.instance) {
      PlatformService.instance = new PlatformService()
    }
    return PlatformService.instance
  }

  /**
   * 创建平台
   */
  async create(input: CreatePlatformInput): Promise<DatabaseResult<Platform>> {
    try {
      await dexieDatabase.initialize()

      const data: Omit<Platform, 'id'> = {
        name: input.name,
        url: input.url,
        icon: input.icon || null,
        icon_blob: input.icon_blob,
        is_delete: 0
      }

      const id = await dexieDatabase.platform.add(data as Platform)
      let record = await dexieDatabase.platform.get(id)

      // 如果没有提供icon_blob，尝试自动获取favicon
      if (record && !record.icon_blob && (record.icon || record.url)) {
        console.log(`【PlatformService】自动获取favicon: ${record.name}`)
        try {
          const faviconUrl = record.icon || record.url
          const faviconResult = await faviconService.getFaviconFromUrl(faviconUrl)

          if (faviconResult.success && faviconResult.blob) {
            // 更新记录包含BLOB数据
            await dexieDatabase.platform.update(id, {
              icon_blob: faviconResult.blob,
              icon: faviconResult.source || record.icon
            })

            // 重新获取更新后的记录
            record = await dexieDatabase.platform.get(id)
            console.log(`【PlatformService】成功获取并存储favicon: ${record?.name}`)
          }
        } catch (faviconError) {
          console.warn(`【PlatformService】获取favicon失败: ${record.name}`, faviconError)
          // 不影响平台创建，继续返回成功
        }
      }

      return {
        success: true,
        data: record!
      }
    } catch (error) {
      console.error('Create platform error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据ID获取平台
   */
  async getById(id: number): Promise<DatabaseResult<Platform>> {
    try {
      await dexieDatabase.initialize()
      
      const record = await dexieDatabase.platform
        .where('id')
        .equals(id)
        .and(item => item.is_delete === 0)
        .first()

      if (!record) {
        return {
          success: false,
          error: 'Platform not found'
        }
      }

      return {
        success: true,
        data: record
      }
    } catch (error) {
      console.error('Get platform by id error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据名称获取平台
   */
  async getByName(name: string): Promise<DatabaseResult<Platform>> {
    try {
      await dexieDatabase.initialize()
      
      const record = await dexieDatabase.platform
        .where('name')
        .equals(name)
        .and(item => item.is_delete === 0)
        .first()

      if (!record) {
        return {
          success: false,
          error: 'Platform not found'
        }
      }

      return {
        success: true,
        data: record
      }
    } catch (error) {
      console.error('Get platform by name error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据URL获取平台
   */
  async getByUrl(url: string): Promise<DatabaseResult<Platform>> {
    try {
      await dexieDatabase.initialize()
      
      const record = await dexieDatabase.platform
        .where('url')
        .equals(url)
        .and(item => item.is_delete === 0)
        .first()

      if (!record) {
        return {
          success: false,
          error: 'Platform not found'
        }
      }

      return {
        success: true,
        data: record
      }
    } catch (error) {
      console.error('Get platform by url error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取所有平台
   */
  async getAll(): Promise<DatabaseResult<Platform[]>> {
    try {
      await dexieDatabase.initialize()
      
      const records = await dexieDatabase.platform
        .where('is_delete')
        .equals(0)
        .toArray()

      return {
        success: true,
        data: records
      }
    } catch (error) {
      console.error('Get all platforms error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 更新平台
   */
  async update(id: number, input: UpdatePlatformInput): Promise<DatabaseResult<Platform>> {
    try {
      await dexieDatabase.initialize()

      await dexieDatabase.platform.update(id, input)
      let record = await dexieDatabase.platform.get(id)

      if (!record) {
        return {
          success: false,
          error: 'Platform not found after update'
        }
      }

      // 如果更新了icon或url，且没有提供icon_blob，尝试自动获取favicon
      const iconChanged = input.icon !== undefined
      const urlChanged = input.url !== undefined
      const blobProvided = input.icon_blob !== undefined

      if ((iconChanged || urlChanged) && !blobProvided && !record.icon_blob) {
        console.log(`【PlatformService】检测到图标变更，自动获取favicon: ${record.name}`)
        try {
          const faviconUrl = record.icon || record.url
          const faviconResult = await faviconService.getFaviconFromUrl(faviconUrl)

          if (faviconResult.success && faviconResult.blob) {
            // 更新记录包含BLOB数据
            await dexieDatabase.platform.update(id, {
              icon_blob: faviconResult.blob,
              icon: faviconResult.source || record.icon
            })

            // 重新获取更新后的记录
            record = await dexieDatabase.platform.get(id)
            console.log(`【PlatformService】成功更新favicon: ${record?.name}`)
          }
        } catch (faviconError) {
          console.warn(`【PlatformService】更新favicon失败: ${record.name}`, faviconError)
          // 不影响平台更新，继续返回成功
        }
      }

      return {
        success: true,
        data: record
      }
    } catch (error) {
      console.error('Update platform error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 软删除平台
   */
  async delete(id: number): Promise<DatabaseResult<boolean>> {
    try {
      await dexieDatabase.initialize()
      
      await dexieDatabase.platform.update(id, { is_delete: 1 })

      return {
        success: true,
        data: true
      }
    } catch (error) {
      console.error('Delete platform error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 根据域名匹配平台
   */
  async findByDomain(hostname: string): Promise<DatabaseResult<Platform>> {
    try {
      await dexieDatabase.initialize()
      
      const platforms = await dexieDatabase.platform
        .where('is_delete')
        .equals(0)
        .toArray()

      // 查找匹配的平台
      const matchedPlatform = platforms.find(platform => {
        try {
          const platformUrl = new URL(platform.url)
          return platformUrl.hostname === hostname || 
                 hostname.includes(platformUrl.hostname.replace('www.', ''))
        } catch {
          return false
        }
      })

      if (!matchedPlatform) {
        return {
          success: false,
          error: 'No matching platform found'
        }
      }

      return {
        success: true,
        data: matchedPlatform
      }
    } catch (error) {
      console.error('Find platform by domain error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 确保平台存在，不存在则创建
   */
  async ensurePlatform(name: string, url: string, icon?: string): Promise<DatabaseResult<Platform>> {
    try {
      // 先尝试根据名称获取
      const existingResult = await this.getByName(name)
      if (existingResult.success) {
        return existingResult
      }

      // 不存在则创建
      return await this.create({
        name,
        url,
        icon
      })
    } catch (error) {
      console.error('Ensure platform error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 刷新平台favicon（强制重新获取）
   */
  async refreshPlatformFavicon(id: number): Promise<DatabaseResult<Platform>> {
    try {
      await dexieDatabase.initialize()

      const record = await dexieDatabase.platform.get(id)
      if (!record) {
        return {
          success: false,
          error: 'Platform not found'
        }
      }

      console.log(`【PlatformService】刷新favicon: ${record.name}`)

      // 清除现有的BLOB数据
      await dexieDatabase.platform.update(id, {
        icon_blob: undefined
      })

      // 重新获取favicon
      const faviconUrl = record.icon || record.url
      const faviconResult = await faviconService.getFaviconFromUrl(faviconUrl)

      if (faviconResult.success && faviconResult.blob) {
        await dexieDatabase.platform.update(id, {
          icon_blob: faviconResult.blob,
          icon: faviconResult.source || record.icon
        })

        const updatedRecord = await dexieDatabase.platform.get(id)
        console.log(`【PlatformService】成功刷新favicon: ${record.name}`)

        return {
          success: true,
          data: updatedRecord!
        }
      } else {
        return {
          success: false,
          error: faviconResult.error || 'Failed to fetch favicon'
        }
      }

    } catch (error) {
      console.error('Refresh platform favicon error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }
}

// 导出单例实例
export const platformService = PlatformService.getInstance()
