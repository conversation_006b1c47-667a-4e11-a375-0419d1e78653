/**
 * Favicon功能测试脚本
 * 用于验证favicon获取、BLOB存储和图标显示功能
 */

import { faviconService } from '../lib/service/faviconService'
import { iconBlobService } from '../lib/service/iconBlobService'
import { platformService } from '../lib/service/platformDexie'
import { platformMigrationService } from '../lib/service/platformMigrationService'
import { PlatformIcon } from '../components/PlatformIcon'

export class FaviconTest {
  private testResults: Array<{
    test: string
    success: boolean
    message: string
    duration: number
  }> = []

  /**
   * 运行所有测试
   */
  async runAllTests(): Promise<void> {
    console.log('【FaviconTest】开始运行favicon功能测试')
    
    try {
      await this.testFaviconService()
      await this.testIconBlobService()
      await this.testPlatformService()
      await this.testMigrationService()
      await this.testPlatformIcon()
      await this.testBackwardCompatibility()
      
      this.printTestResults()
      
    } catch (error) {
      console.error('【FaviconTest】测试运行失败:', error)
    }
  }

  /**
   * 测试FaviconService
   */
  private async testFaviconService(): Promise<void> {
    console.log('【FaviconTest】测试FaviconService...')

    // 测试1: 从URL获取favicon
    await this.runTest('FaviconService - 获取GitHub favicon', async () => {
      const result = await faviconService.getFaviconFromUrl('https://github.com')
      if (!result.success) {
        throw new Error(result.error || 'Failed to get favicon')
      }
      if (!result.blob || !result.dataUrl) {
        throw new Error('Missing blob or dataUrl')
      }
      return `获取成功，大小: ${result.blob.size} bytes`
    })

    // 测试2: 验证favicon
    await this.runTest('FaviconService - 验证favicon', async () => {
      const result = await faviconService.getFaviconFromUrl('https://www.google.com')
      if (!result.success || !result.blob) {
        throw new Error('Failed to get favicon for validation')
      }
      
      const isValid = await faviconService.validateFavicon(result.blob)
      if (!isValid) {
        throw new Error('Favicon validation failed')
      }
      return '验证通过'
    })
  }

  /**
   * 测试IconBlobService
   */
  private async testIconBlobService(): Promise<void> {
    console.log('【FaviconTest】测试IconBlobService...')

    // 创建测试平台
    const testPlatform = {
      id: 999,
      name: 'TestPlatform',
      url: 'https://www.example.com',
      icon: 'https://www.example.com/favicon.ico',
      is_delete: 0
    }

    await this.runTest('IconBlobService - 获取平台图标', async () => {
      const result = await iconBlobService.getPlatformIcon(testPlatform)
      if (!result.success) {
        throw new Error(result.error || 'Failed to get platform icon')
      }
      return `获取成功，来源: ${result.fromCache ? '缓存' : '网络'}`
    })
  }

  /**
   * 测试PlatformService
   */
  private async testPlatformService(): Promise<void> {
    console.log('【FaviconTest】测试PlatformService...')

    await this.runTest('PlatformService - 创建带favicon的平台', async () => {
      const result = await platformService.create({
        name: 'TestPlatform2',
        url: 'https://www.npmjs.com'
      })
      
      if (!result.success) {
        throw new Error(result.error || 'Failed to create platform')
      }
      
      // 检查是否自动获取了favicon
      const platform = result.data!
      const hasBlob = !!platform.icon_blob
      
      // 清理测试数据
      if (platform.id) {
        await platformService.delete(platform.id)
      }
      
      return `创建成功，自动获取favicon: ${hasBlob ? '是' : '否'}`
    })
  }

  /**
   * 测试MigrationService
   */
  private async testMigrationService(): Promise<void> {
    console.log('【FaviconTest】测试MigrationService...')

    await this.runTest('MigrationService - 检查迁移需求', async () => {
      const result = await platformMigrationService.checkMigrationNeeded()
      return `需要迁移: ${result.needed ? '是' : '否'}, 平台数: ${result.platformsToMigrate.length}/${result.totalCount}`
    })

    await this.runTest('MigrationService - 验证迁移状态', async () => {
      const result = await platformMigrationService.validateMigration()
      return `总平台: ${result.totalPlatforms}, 有BLOB: ${result.withBlob}, 无BLOB: ${result.withoutBlob}`
    })
  }

  /**
   * 测试PlatformIcon组件
   */
  private async testPlatformIcon(): Promise<void> {
    console.log('【FaviconTest】测试PlatformIcon组件...')

    await this.runTest('PlatformIcon - 创建图标元素', async () => {
      const platformIcon = new PlatformIcon()
      const testPlatform = {
        id: 1,
        name: 'DeepSeek',
        url: 'https://chat.deepseek.com',
        icon: 'https://chat.deepseek.com/favicon.ico',
        is_delete: 0
      }
      
      const iconElement = platformIcon.createIcon(testPlatform)
      if (!iconElement || !iconElement.dataset.platformId) {
        throw new Error('Failed to create icon element')
      }
      
      return `图标元素创建成功，平台ID: ${iconElement.dataset.platformId}`
    })
  }

  /**
   * 测试向后兼容性
   */
  private async testBackwardCompatibility(): Promise<void> {
    console.log('【FaviconTest】测试向后兼容性...')

    await this.runTest('向后兼容性 - 处理无BLOB的平台', async () => {
      // 创建一个没有BLOB的平台
      const result = await platformService.create({
        name: 'LegacyPlatform',
        url: 'https://www.github.com',
        icon: 'https://www.github.com/favicon.ico'
      })
      
      if (!result.success) {
        throw new Error('Failed to create legacy platform')
      }
      
      const platform = result.data!
      
      // 清除BLOB数据模拟旧数据
      await platformService.update(platform.id!, {
        icon_blob: undefined
      })
      
      // 测试图标获取是否正常工作
      const iconResult = await iconBlobService.getPlatformIcon(platform)
      
      // 清理测试数据
      if (platform.id) {
        await platformService.delete(platform.id)
      }
      
      if (!iconResult.success) {
        throw new Error('Failed to handle legacy platform')
      }
      
      return '向后兼容性测试通过'
    })
  }

  /**
   * 运行单个测试
   */
  private async runTest(testName: string, testFn: () => Promise<string>): Promise<void> {
    const startTime = Date.now()
    
    try {
      const message = await testFn()
      const duration = Date.now() - startTime
      
      this.testResults.push({
        test: testName,
        success: true,
        message,
        duration
      })
      
      console.log(`✓ ${testName}: ${message} (${duration}ms)`)
      
    } catch (error) {
      const duration = Date.now() - startTime
      const message = error instanceof Error ? error.message : 'Unknown error'
      
      this.testResults.push({
        test: testName,
        success: false,
        message,
        duration
      })
      
      console.error(`✗ ${testName}: ${message} (${duration}ms)`)
    }
  }

  /**
   * 打印测试结果
   */
  private printTestResults(): void {
    const totalTests = this.testResults.length
    const passedTests = this.testResults.filter(r => r.success).length
    const failedTests = totalTests - passedTests
    const totalDuration = this.testResults.reduce((sum, r) => sum + r.duration, 0)
    
    console.log('\n【FaviconTest】测试结果汇总:')
    console.log(`总测试数: ${totalTests}`)
    console.log(`通过: ${passedTests}`)
    console.log(`失败: ${failedTests}`)
    console.log(`总耗时: ${totalDuration}ms`)
    console.log(`成功率: ${((passedTests / totalTests) * 100).toFixed(1)}%`)
    
    if (failedTests > 0) {
      console.log('\n失败的测试:')
      this.testResults
        .filter(r => !r.success)
        .forEach(r => console.log(`- ${r.test}: ${r.message}`))
    }
  }

  /**
   * 获取测试结果
   */
  getTestResults(): typeof this.testResults {
    return [...this.testResults]
  }
}

// 导出测试实例
export const faviconTest = new FaviconTest()

// 如果在浏览器环境中，添加到全局对象以便调试
if (typeof window !== 'undefined') {
  (window as any).faviconTest = faviconTest
}
