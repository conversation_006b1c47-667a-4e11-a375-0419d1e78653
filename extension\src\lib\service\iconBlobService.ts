/**
 * 图标BLOB存储服务
 * 负责favicon的BLOB存储、读取和转换
 */

import { faviconService, FaviconResult } from './faviconService'
import { platformService } from './platformDexie'
import { Platform } from '@/types/database'

export interface IconBlobResult {
  success: boolean
  dataUrl?: string
  blob?: Blob
  error?: string
  fromCache?: boolean
}

export class IconBlobService {
  private static instance: IconBlobService
  private blobCache: Map<number, string> = new Map() // platformId -> dataUrl
  private loadingSet: Set<number> = new Set()

  private constructor() {}

  static getInstance(): IconBlobService {
    if (!IconBlobService.instance) {
      IconBlobService.instance = new IconBlobService()
    }
    return IconBlobService.instance
  }

  /**
   * 获取平台图标（优先使用BLOB，fallback到URL）
   */
  async getPlatformIcon(platform: Platform): Promise<IconBlobResult> {
    try {
      const platformId = platform.id!
      
      // 检查内存缓存
      if (this.blobCache.has(platformId)) {
        return {
          success: true,
          dataUrl: this.blobCache.get(platformId)!,
          fromCache: true
        }
      }

      // 检查是否正在加载
      if (this.loadingSet.has(platformId)) {
        // 等待加载完成
        await this.waitForLoading(platformId)
        if (this.blobCache.has(platformId)) {
          return {
            success: true,
            dataUrl: this.blobCache.get(platformId)!,
            fromCache: true
          }
        }
      }

      this.loadingSet.add(platformId)

      try {
        // 1. 优先使用数据库中的BLOB数据
        if (platform.icon_blob) {
          console.log(`【IconBlobService】使用数据库BLOB数据: ${platform.name}`)
          const dataUrl = await this.blobToDataUrl(platform.icon_blob)
          this.blobCache.set(platformId, dataUrl)
          return {
            success: true,
            dataUrl,
            blob: platform.icon_blob
          }
        }

        // 2. 如果没有BLOB，尝试从URL获取并存储
        if (platform.icon || platform.url) {
          console.log(`【IconBlobService】从URL获取favicon: ${platform.name}`)
          const faviconResult = await this.fetchAndStoreFavicon(platform)
          if (faviconResult.success && faviconResult.dataUrl) {
            this.blobCache.set(platformId, faviconResult.dataUrl)
            return faviconResult
          }
        }

        // 3. 都失败了，返回失败
        return {
          success: false,
          error: 'No valid icon source found'
        }

      } finally {
        this.loadingSet.delete(platformId)
      }

    } catch (error) {
      console.error('【IconBlobService】获取平台图标失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 获取并存储favicon
   */
  private async fetchAndStoreFavicon(platform: Platform): Promise<IconBlobResult> {
    try {
      let faviconResult: FaviconResult

      // 如果有icon URL，直接使用
      if (platform.icon) {
        faviconResult = await faviconService.getFaviconFromUrl(platform.icon)
      } else {
        // 否则从平台URL获取
        faviconResult = await faviconService.getFaviconFromUrl(platform.url)
      }

      if (!faviconResult.success || !faviconResult.blob || !faviconResult.dataUrl) {
        return {
          success: false,
          error: faviconResult.error || 'Failed to fetch favicon'
        }
      }

      // 存储到数据库
      const updateResult = await platformService.update(platform.id!, {
        icon_blob: faviconResult.blob,
        icon: faviconResult.source // 更新icon URL为实际获取的URL
      })

      if (!updateResult.success) {
        console.warn('【IconBlobService】存储BLOB到数据库失败:', updateResult.error)
        // 即使存储失败，也返回获取到的数据
      }

      return {
        success: true,
        dataUrl: faviconResult.dataUrl,
        blob: faviconResult.blob
      }

    } catch (error) {
      console.error('【IconBlobService】获取并存储favicon失败:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  /**
   * 批量更新平台图标
   */
  async batchUpdatePlatformIcons(platforms: Platform[]): Promise<{
    success: number
    failed: number
    errors: string[]
  }> {
    const results = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    }

    console.log(`【IconBlobService】开始批量更新${platforms.length}个平台图标`)

    for (const platform of platforms) {
      try {
        // 跳过已有BLOB的平台
        if (platform.icon_blob) {
          console.log(`【IconBlobService】跳过已有BLOB的平台: ${platform.name}`)
          results.success++
          continue
        }

        const result = await this.fetchAndStoreFavicon(platform)
        if (result.success) {
          results.success++
          console.log(`【IconBlobService】成功更新平台图标: ${platform.name}`)
        } else {
          results.failed++
          results.errors.push(`${platform.name}: ${result.error}`)
          console.error(`【IconBlobService】更新平台图标失败: ${platform.name} - ${result.error}`)
        }

        // 添加延迟避免请求过于频繁
        await this.delay(500)

      } catch (error) {
        results.failed++
        const errorMsg = error instanceof Error ? error.message : 'Unknown error'
        results.errors.push(`${platform.name}: ${errorMsg}`)
        console.error(`【IconBlobService】处理平台失败: ${platform.name}`, error)
      }
    }

    console.log(`【IconBlobService】批量更新完成: 成功${results.success}个, 失败${results.failed}个`)
    return results
  }

  /**
   * 刷新平台图标（强制重新获取）
   */
  async refreshPlatformIcon(platform: Platform): Promise<IconBlobResult> {
    // 清除缓存
    this.blobCache.delete(platform.id!)
    
    // 清除数据库中的BLOB
    await platformService.update(platform.id!, {
      icon_blob: undefined
    })

    // 重新获取
    return this.getPlatformIcon(platform)
  }

  /**
   * 将Blob转换为Data URL
   */
  private blobToDataUrl(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = () => reject(new Error('Failed to convert blob to data URL'))
      reader.readAsDataURL(blob)
    })
  }

  /**
   * 等待加载完成
   */
  private async waitForLoading(platformId: number, timeout = 10000): Promise<void> {
    const startTime = Date.now()
    while (this.loadingSet.has(platformId)) {
      if (Date.now() - startTime > timeout) {
        throw new Error('Loading timeout')
      }
      await this.delay(100)
    }
  }

  /**
   * 延迟函数
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.blobCache.clear()
    this.loadingSet.clear()
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): {
    cacheSize: number
    loadingCount: number
  } {
    return {
      cacheSize: this.blobCache.size,
      loadingCount: this.loadingSet.size
    }
  }
}

// 导出单例实例
export const iconBlobService = IconBlobService.getInstance()
